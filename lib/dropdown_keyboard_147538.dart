import 'package:flutter/material.dart';

void main() {
  runApp(const DropdownMenuExample());
}

enum ColorLabel {
  blue('Blue', Colors.blue),
  pink('Pink', Colors.pink),
  green('Green', Colors.green),
  yellow('Orange', Colors.orange),
  grey('Grey', Colors.grey);

  const ColorLabel(this.label, this.color);
  final String label;
  final Color color;
}

class DropdownMenuExample extends StatefulWidget {
  const DropdownMenuExample({super.key});
  @override
  State<DropdownMenuExample> createState() => _DropdownMenuExampleState();
}

class _DropdownMenuExampleState extends State<DropdownMenuExample> {
  final TextEditingController colorController = TextEditingController();
  ColorLabel? selectedColor;
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: ThemeData(useMaterial3: true, colorSchemeSeed: Colors.green),
      home: Scaffold(
        body: <PERSON><PERSON>rea(
          child: ListView(
            // padding: EdgeInsets.only(
            //   bottom: MediaQuery.viewInsetsOf(context).bottom,
            // ),
            children: <Widget>[
              TextField(),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    DropdownMenu<ColorLabel>(
                      initialSelection: ColorLabel.green,
                      controller: colorController,
                      requestFocusOnTap: true,
                      label: const Text('Color'),
                      onSelected: (ColorLabel? color) {
                        setState(() {
                          selectedColor = color;
                        });
                      },
                      dropdownMenuEntries:
                          ColorLabel.values.map<DropdownMenuEntry<ColorLabel>>((
                            ColorLabel color,
                          ) {
                            return DropdownMenuEntry<ColorLabel>(
                              value: color,
                              label: color.label,
                              enabled: color.label != 'Grey',
                              style: MenuItemButton.styleFrom(
                                foregroundColor: color.color,
                              ),
                            );
                          }).toList(),
                    ),
                    const SizedBox(height: 710),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
