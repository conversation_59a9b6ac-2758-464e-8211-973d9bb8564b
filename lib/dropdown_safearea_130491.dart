import 'package:flutter/material.dart';

void main() {
  runApp(
    MaterialApp(
      theme: ThemeData.dark(useMaterial3: true),
      home: const MyHomePage(title: 'Dropdown Demo'),
    ),
  );
}

class MyHomePage extends StatefulWidget {
  final String title;

  const MyHomePage({super.key, required this.title});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.title), backgroundColor: Colors.blueAccent),
      body: Overlay.wrap(
        child: Center(
          child: DropdownMenu(
            dropdownMenuEntries: [
              for (final item in Iterable.generate(100))
                DropdownMenuEntry(value: item, label: 'Item $item'),
            ],
          ),
        ),
      ),
    );
  }
}
