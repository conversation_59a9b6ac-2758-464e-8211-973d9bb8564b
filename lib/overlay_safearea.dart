import 'package:flutter/material.dart';

main() {
  runApp(
    MaterialApp(
      home: OverlayDemo(),
    ),
  );
}

class OverlayDemo extends StatelessWidget {
  const OverlayDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('AppBar + Overlay'),
      ),
      body: Safe<PERSON>rea(child: Overlay.wrap(child: OverlayWidget()))
    );
  }
}


class OverlayWidget extends StatefulWidget {
  const OverlayWidget({
    super.key,
  });

  @override
  _OverlayWidgetState createState() => _OverlayWidgetState();
}

class _OverlayWidgetState extends State<OverlayWidget> {
  OverlayEntry? overlayEntry;

  void showOverlay() {
    if (overlayEntry == null) {
      overlayEntry = OverlayEntry(
          builder: (BuildContext context) {
            return Positioned(
                top: 0.0,
                left: 0.0,
                child: Container(
                  width: 100,
                  height: 100,
                  color: Colors.blue,
                )
            );
          }
      );
      Overlay.of(context).insert(overlayEntry!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: showOverlay,
      child: Text('Show Overlay')
    );
  }
}