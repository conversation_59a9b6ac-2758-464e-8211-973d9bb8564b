import 'package:flutter/material.dart';

void main() {
  runApp(const DropdownTestApp());
}

// Custom DropdownMenu that properly handles disabled state
class ProperlyDisabledDropdownMenu<T> extends StatelessWidget {
  final bool enabled;
  final double? width;
  final T? initialSelection;
  final List<DropdownMenuEntry<T>> dropdownMenuEntries;
  final ValueChanged<T?>? onSelected;
  final String labelText;

  const ProperlyDisabledDropdownMenu({
    super.key,
    required this.enabled,
    this.width,
    this.initialSelection,
    required this.dropdownMenuEntries,
    this.onSelected,
    required this.labelText,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final disabledColor = theme.disabledColor;
    final primaryColor = theme.colorScheme.primary;
    final onSurfaceColor = theme.colorScheme.onSurface;

    return Opacity(
      opacity: enabled ? 1.0 : 0.6,
      child: IgnorePointer(
        ignoring: !enabled,
        child: DropdownMenu<T>(
          width: width,
          enabled: true, // Always true, we handle disabling with IgnorePointer
          initialSelection: initialSelection,
          onSelected: enabled ? onSelected : null,
          label: Text(
            labelText,
            style: TextStyle(
              color: enabled ? null : disabledColor,
            ),
          ),
          inputDecorationTheme: InputDecorationTheme(
            labelStyle: TextStyle(
              color: enabled ? onSurfaceColor : disabledColor,
            ),
            floatingLabelStyle: TextStyle(
              color: enabled ? primaryColor : disabledColor,
            ),
            enabledBorder: enabled ? null : OutlineInputBorder(
              borderSide: BorderSide(color: disabledColor),
            ),
            focusedBorder: enabled ? null : OutlineInputBorder(
              borderSide: BorderSide(color: disabledColor),
            ),
          ),
          dropdownMenuEntries: dropdownMenuEntries,
        ),
      ),
    );
  }
}

class DropdownTestApp extends StatelessWidget {
  const DropdownTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'DropdownMenu Disabled Test',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const DropdownTestPage(),
    );
  }
}

class DropdownTestPage extends StatefulWidget {
  const DropdownTestPage({super.key});

  @override
  State<DropdownTestPage> createState() => _DropdownTestPageState();
}

class _DropdownTestPageState extends State<DropdownTestPage> {
  bool enabled = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('DropdownMenu Disabled Test'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current state: ${enabled ? "Enabled" : "Disabled"}',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 20),
            
            // Problem: Default DropdownMenu with enabled: false
            Text('Problem: Default DropdownMenu (enabled: $enabled)'),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.red, width: 1),
              ),
              child: DropdownMenu<String>(
                width: double.infinity,
                enabled: enabled,
                initialSelection: 'One',
                label: const Text('Default DropdownMenu'),
                dropdownMenuEntries: const [
                  DropdownMenuEntry(value: 'One', label: 'One'),
                  DropdownMenuEntry(value: 'Two', label: 'Two'),
                  DropdownMenuEntry(value: 'Three', label: 'Three'),
                ],
              ),
            ),
            
            const SizedBox(height: 30),
            
            // Solution 1: Using inputDecorationTheme with explicit colors
            const Text('Solution 1: Using inputDecorationTheme'),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.blue, width: 1),
              ),
              child: DropdownMenu<String>(
                width: double.infinity,
                enabled: enabled,
                initialSelection: 'One',
                label: Text(
                  'With InputDecorationTheme',
                  style: TextStyle(
                    color: enabled
                        ? Theme.of(context).colorScheme.onSurface
                        : Theme.of(context).disabledColor,
                  ),
                ),
                inputDecorationTheme: InputDecorationTheme(
                  labelStyle: TextStyle(
                    color: enabled
                        ? Theme.of(context).colorScheme.onSurface
                        : Theme.of(context).disabledColor,
                  ),
                  floatingLabelStyle: TextStyle(
                    color: enabled
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).disabledColor,
                  ),
                  disabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: Theme.of(context).disabledColor,
                    ),
                  ),
                ),
                dropdownMenuEntries: const [
                  DropdownMenuEntry(value: 'One', label: 'One'),
                  DropdownMenuEntry(value: 'Two', label: 'Two'),
                  DropdownMenuEntry(value: 'Three', label: 'Three'),
                ],
              ),
            ),
            
            const SizedBox(height: 30),
            
            // Solution 2: Working solution with proper label styling
            const Text('Solution 2: Working solution'),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.green, width: 1),
              ),
              child: Opacity(
                opacity: enabled ? 1.0 : 0.6,
                child: IgnorePointer(
                  ignoring: !enabled,
                  child: DropdownMenu<String>(
                    width: double.infinity,
                    enabled: true, // Always enabled, but we control interaction with IgnorePointer
                    initialSelection: 'One',
                    label: Text(
                      'Working Solution',
                      style: TextStyle(
                        color: enabled
                            ? null // Use default theme color
                            : Theme.of(context).disabledColor,
                      ),
                    ),
                    inputDecorationTheme: InputDecorationTheme(
                      labelStyle: TextStyle(
                        color: enabled
                            ? null
                            : Theme.of(context).disabledColor,
                      ),
                      floatingLabelStyle: TextStyle(
                        color: enabled
                            ? null
                            : Theme.of(context).disabledColor,
                      ),
                      border: enabled
                          ? null
                          : OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Theme.of(context).disabledColor,
                              ),
                            ),
                    ),
                    dropdownMenuEntries: const [
                      DropdownMenuEntry(value: 'One', label: 'One'),
                      DropdownMenuEntry(value: 'Two', label: 'Two'),
                      DropdownMenuEntry(value: 'Three', label: 'Three'),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 30),

            // Solution 3: Custom widget
            const Text('Solution 3: Custom ProperlyDisabledDropdownMenu'),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.purple, width: 1),
              ),
              child: ProperlyDisabledDropdownMenu<String>(
                width: double.infinity,
                enabled: enabled,
                initialSelection: 'One',
                labelText: 'Custom Widget Solution',
                dropdownMenuEntries: const [
                  DropdownMenuEntry(value: 'One', label: 'One'),
                  DropdownMenuEntry(value: 'Two', label: 'Two'),
                  DropdownMenuEntry(value: 'Three', label: 'Three'),
                ],
              ),
            ),

            const SizedBox(height: 30),

            // Comparison: TextField (works correctly)
            const Text('Comparison: TextField (works correctly)'),
            const SizedBox(height: 8),
            TextField(
              enabled: enabled,
              decoration: const InputDecoration(
                labelText: 'TextField for comparison',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          setState(() {
            enabled = !enabled;
          });
        },
        tooltip: 'Toggle enabled state',
        child: Icon(enabled ? Icons.toggle_on : Icons.toggle_off),
      ),
    );
  }
}
