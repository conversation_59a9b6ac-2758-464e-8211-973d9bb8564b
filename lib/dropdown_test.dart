import 'package:flutter/material.dart';

void main() {
  runApp(const DropdownTestApp());
}

class DropdownTestApp extends StatelessWidget {
  const DropdownTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'DropdownMenu Disabled Test',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const DropdownTestPage(),
    );
  }
}

class DropdownTestPage extends StatefulWidget {
  const DropdownTestPage({super.key});

  @override
  State<DropdownTestPage> createState() => _DropdownTestPageState();
}

class _DropdownTestPageState extends State<DropdownTestPage> {
  bool enabled = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('DropdownMenu Disabled Test'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current state: ${enabled ? "Enabled" : "Disabled"}',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 20),
            
            // Problem: Default DropdownMenu with enabled: false
            const Text('Problem: Default DropdownMenu (label styling issue)'),
            const SizedBox(height: 8),
            DropdownMenu<String>(
              width: double.infinity,
              enabled: enabled,
              initialSelection: 'One',
              label: const Text('Default DropdownMenu'),
              dropdownMenuEntries: const [
                DropdownMenuEntry(value: 'One', label: 'One'),
                DropdownMenuEntry(value: 'Two', label: 'Two'),
                DropdownMenuEntry(value: 'Three', label: 'Three'),
              ],
            ),
            
            const SizedBox(height: 30),
            
            // Solution 1: Using inputDecorationTheme
            const Text('Solution 1: Using inputDecorationTheme'),
            const SizedBox(height: 8),
            DropdownMenu<String>(
              width: double.infinity,
              enabled: enabled,
              initialSelection: 'One',
              label: const Text('With InputDecorationTheme'),
              inputDecorationTheme: InputDecorationTheme(
                labelStyle: TextStyle(
                  color: enabled 
                      ? null // Use default theme color
                      : Theme.of(context).disabledColor,
                ),
                floatingLabelStyle: TextStyle(
                  color: enabled 
                      ? null // Use default theme color
                      : Theme.of(context).disabledColor,
                ),
              ),
              dropdownMenuEntries: const [
                DropdownMenuEntry(value: 'One', label: 'One'),
                DropdownMenuEntry(value: 'Two', label: 'Two'),
                DropdownMenuEntry(value: 'Three', label: 'Three'),
              ],
            ),
            
            const SizedBox(height: 30),
            
            // Solution 2: Using Theme wrapper
            const Text('Solution 2: Using Theme wrapper'),
            const SizedBox(height: 8),
            Theme(
              data: Theme.of(context).copyWith(
                inputDecorationTheme: InputDecorationTheme(
                  labelStyle: TextStyle(
                    color: enabled 
                        ? null
                        : Theme.of(context).disabledColor,
                  ),
                  floatingLabelStyle: TextStyle(
                    color: enabled 
                        ? null
                        : Theme.of(context).disabledColor,
                  ),
                ),
              ),
              child: DropdownMenu<String>(
                width: double.infinity,
                enabled: enabled,
                initialSelection: 'One',
                label: const Text('With Theme Override'),
                dropdownMenuEntries: const [
                  DropdownMenuEntry(value: 'One', label: 'One'),
                  DropdownMenuEntry(value: 'Two', label: 'Two'),
                  DropdownMenuEntry(value: 'Three', label: 'Three'),
                ],
              ),
            ),
            
            const SizedBox(height: 30),
            
            // Comparison: TextField (works correctly)
            const Text('Comparison: TextField (works correctly)'),
            const SizedBox(height: 8),
            TextField(
              enabled: enabled,
              decoration: const InputDecoration(
                labelText: 'TextField for comparison',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          setState(() {
            enabled = !enabled;
          });
        },
        tooltip: 'Toggle enabled state',
        child: Icon(enabled ? Icons.toggle_on : Icons.toggle_off),
      ),
    );
  }
}
