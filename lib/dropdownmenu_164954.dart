import 'package:flutter/material.dart';

void main() {
  runApp(const DropdownMenuExample());
}

class DropdownMenuExample extends StatefulWidget {
  const DropdownMenuExample({super.key});

  @override
  State<DropdownMenuExample> createState() => _DropdownMenuExampleState();
}

class _DropdownMenuExampleState extends State<DropdownMenuExample> {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: ThemeData(useMaterial3: true, colorSchemeSeed: Colors.green),
      home: Scaffold(
        body: SafeArea(
          child: Center(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        DropdownMenu<int>(
                          initialSelection: 0,
                          width: 250,
                          inputDecorationTheme: const InputDecorationTheme(
                            isDense: true,
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.all(0.0),
                          ),
                          dropdownMenuEntries: [
                            DropdownMenuEntry(
                              value: 0,
                              label: "contentPadding: 0.0",
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                        DropdownMenu<int>(
                          initialSelection: 0,
                          width: 250,
                          inputDecorationTheme: const InputDecorationTheme(
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.all(12.0),
                          ),
                          dropdownMenuEntries: [
                            DropdownMenuEntry(
                              value: 0,
                              label: "contentPadding: 12.0",
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                        DropdownMenu<int>(
                          initialSelection: 0,
                          width: 250,
                          inputDecorationTheme: const InputDecorationTheme(
                            isDense: true,
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.all(16.0),
                          ),
                          dropdownMenuEntries: [
                            DropdownMenuEntry(
                              value: 0,
                              label: "contentPadding: 16.0",
                            ),
                          ],
                        ),
                        const SizedBox(height: 24),
                        DropdownMenu<int>(
                          initialSelection: 0,
                          width: 360,
                          inputDecorationTheme: const InputDecorationTheme(
                            isDense: true,
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.all(30.0),
                          ),
                          dropdownMenuEntries: [
                            DropdownMenuEntry(
                              value: 0,
                              label: "contentPadding: 30.0",
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
