import 'package:flutter/material.dart';

void main() {
  runApp(const FrameworkFixTestApp());
}

class FrameworkFixTestApp extends StatelessWidget {
  const FrameworkFixTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Framework Fix Test',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const FrameworkFixTestPage(),
    );
  }
}

class FrameworkFixTestPage extends StatefulWidget {
  const FrameworkFixTestPage({super.key});

  @override
  State<FrameworkFixTestPage> createState() => _FrameworkFixTestPageState();
}

class _FrameworkFixTestPageState extends State<FrameworkFixTestPage> {
  bool enabled = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Framework Fix Test'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Testing Framework Fix',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            Text(
              'Current state: ${enabled ? "Enabled" : "Disabled"}',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 20),
            
            // Test the framework fix - this should now work correctly
            const Text('DropdownMenu with Framework Fix:'),
            const SizedBox(height: 8),
            DropdownMenu<String>(
              width: double.infinity,
              enabled: enabled,
              initialSelection: 'One',
              label: const Text('Framework Fixed Label'),
              dropdownMenuEntries: const [
                DropdownMenuEntry(value: 'One', label: 'One'),
                DropdownMenuEntry(value: 'Two', label: 'Two'),
                DropdownMenuEntry(value: 'Three', label: 'Three'),
              ],
            ),
            
            const SizedBox(height: 30),
            
            // Comparison with TextField
            const Text('TextField for comparison:'),
            const SizedBox(height: 8),
            TextField(
              enabled: enabled,
              decoration: const InputDecoration(
                labelText: 'TextField Label',
                border: OutlineInputBorder(),
              ),
            ),
            
            const SizedBox(height: 30),
            
            Text(
              'Instructions:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Text(
              '1. Tap the toggle button to disable both widgets\n'
              '2. Check that both labels appear with disabled styling\n'
              '3. The DropdownMenu label should now match the TextField disabled appearance',
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          setState(() {
            enabled = !enabled;
          });
        },
        label: Text(enabled ? 'Disable' : 'Enable'),
        icon: Icon(enabled ? Icons.toggle_on : Icons.toggle_off),
      ),
    );
  }
}
