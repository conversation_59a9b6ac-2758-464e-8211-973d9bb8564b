import 'package:flutter/material.dart';

void main() => runApp(MyApp());
class MyApp extends StatelessWidget {
  static const String _title = 'Flutter Code Sample';

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: _title,
      theme: ThemeData(
        focusColor: Colors.black.withValues(alpha: 0.12),
      ),
      home: Scaffold(
        appBar: AppBar(title: const Text(_title)),
        body: Center(child: MyStatefulWidget()),
      ),
    );
  }
}

/// This is the stateful widget that the main application instantiates.
class MyStatefulWidget extends StatefulWidget {
  const MyStatefulWidget({Key? key}) : super(key: key);

  @override
  _MyStatefulWidgetState createState() => _MyStatefulWidgetState();
}

/// This is the private State class that goes with MyStatefulWidget.
class _MyStatefulWidgetState extends State<MyStatefulWidget> {
  String dropdownValue = 'one';
 List<String> menuItems = <String>['one', 'two', 'three', 'four'];

  @override
  Widget build(BuildContext context) {
    // FocusManager.instance.highlightStrategy= FocusHighlightStrategy.alwaysTraditional;
    return DropdownButton<String>(
      value: dropdownValue,
      icon: Icon(Icons.arrow_downward),
      iconSize: 24,
      elevation: 16,
      style: TextStyle(color: Colors.deepPurple),
      underline: Container(height: 2, color: Colors.deepPurpleAccent),
      items:menuItems.map<DropdownMenuItem<String>>((String value) {
            return DropdownMenuItem<String>(value: value, child: Text(value));
          }).toList(),
      onChanged: (String? value) {
        setState(() {
          dropdownValue = value ?? 'N/A';
        });
      },
    );
  }
}
