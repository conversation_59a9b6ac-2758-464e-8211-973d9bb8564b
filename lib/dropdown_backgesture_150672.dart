import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const MyHomePage(title: 'Flutter Demo Home Page'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: Text(widget.title),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            DropdownButton(
              items: [
                DropdownMenuItem(child: Text('Dropdown button 1'), value: 1),
                DropdownMenuItem(child: Text('Dropdown button 2'), value: 2),
                DropdownMenuItem(child: Text('Dropdown button 3'), value: 3),
              ],
              onChanged: (value) {},
            ),
            const SizedBox(
              height: 24,
            ),
            DropdownMenu<int>(
              enableSearch: false,
              label: const Text('This is a dropdown menu'),
              width: MediaQuery.of(context).size.width - 32,
              dropdownMenuEntries: const [
                DropdownMenuEntry<int>(
                  value: 1,
                  label: 'Dropdown menu 1',
                ),
                DropdownMenuEntry<int>(
                  value: 2,
                  label: 'Dropdown menu 2',
                ),
                DropdownMenuEntry<int>(
                  value: 3,
                  label: 'Dropdown menu 3',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
