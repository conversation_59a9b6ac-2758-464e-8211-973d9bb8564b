import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const MyHomePage(title: 'Flutter Demo Home Page'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  final TextEditingController textEditingController = TextEditingController(
    text: 'One',
  );
  bool enabled = true;

  void _disable() {
    setState(() {
      enabled = !enabled;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.title)),
      body: Center(
        child: Padding(
          padding: EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              // Test framework fix - both label and selected value should be disabled
              DropdownMenu<String>(
                width: double.infinity,
                enabled: enabled,
                initialSelection: 'One',
                label: Text('Framework Fixed DropdownMenu'),
                dropdownMenuEntries: [
                  DropdownMenuEntry(value: 'One', label: 'One'),
                  DropdownMenuEntry(value: 'Two', label: 'Two'),
                  DropdownMenuEntry(value: 'Three', label: 'Three'),
                ],
              ),

              SizedBox(height: 20),
              TextField(controller: textEditingController, enabled: enabled),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _disable,
        tooltip: 'Update dropdown',
        child: const Icon(Icons.add),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}