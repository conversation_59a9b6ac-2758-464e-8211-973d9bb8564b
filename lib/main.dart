import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

// Custom wrapper widget for better disabled state control
class DisabledDropdownMenu<T> extends StatelessWidget {
  final bool enabled;
  final double? width;
  final T? initialSelection;
  final List<DropdownMenuEntry<T>> dropdownMenuEntries;
  final ValueChanged<T?>? onSelected;
  final Widget? label;

  const DisabledDropdownMenu({
    super.key,
    required this.enabled,
    this.width,
    this.initialSelection,
    required this.dropdownMenuEntries,
    this.onSelected,
    this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: enabled ? 1.0 : 0.6,
      child: IgnorePointer(
        ignoring: !enabled,
        child: DropdownMenu<T>(
          width: width,
          enabled: enabled,
          initialSelection: initialSelection,
          onSelected: enabled ? onSelected : null,
          label: label,
          inputDecorationTheme: InputDecorationTheme(
            labelStyle: TextStyle(
              color: enabled
                  ? null // Use default theme color
                  : Theme.of(context).disabledColor,
            ),
            floatingLabelStyle: TextStyle(
              color: enabled
                  ? null // Use default theme color
                  : Theme.of(context).disabledColor,
            ),
            border: enabled
                ? null
                : OutlineInputBorder(
                    borderSide: BorderSide(
                      color: Theme.of(context).disabledColor,
                    ),
                  ),
          ),
          dropdownMenuEntries: dropdownMenuEntries,
        ),
      ),
    );
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const MyHomePage(title: 'Flutter Demo Home Page'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  final TextEditingController textEditingController = TextEditingController(
    text: 'One',
  );
  bool enabled = true;

  void _disable() {
    setState(() {
      enabled = !enabled;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.title)),
      body: Center(
        child: Padding(
          padding: EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              // Option 1: Using custom wrapper (recommended)
              DisabledDropdownMenu<String>(
                width: double.infinity,
                enabled: enabled,
                initialSelection: 'One',
                label: const Text('Select Option'),
                dropdownMenuEntries: [
                  DropdownMenuEntry(value: 'One', label: 'One'),
                  DropdownMenuEntry(value: 'Two', label: 'Two'),
                  DropdownMenuEntry(value: 'Three', label: 'Three'),
                ],
              ),

              const SizedBox(height: 20),

              // Option 2: Using Theme override
              Theme(
                data: Theme.of(context).copyWith(
                  inputDecorationTheme: InputDecorationTheme(
                    labelStyle: TextStyle(
                      color: enabled
                          ? Theme.of(context).colorScheme.onSurface
                          : Theme.of(context).disabledColor,
                    ),
                    floatingLabelStyle: TextStyle(
                      color: enabled
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).disabledColor,
                    ),
                  ),
                ),
                child: DropdownMenu<String>(
                  width: double.infinity,
                  enabled: enabled,
                  initialSelection: 'One',
                  label: const Text('Theme Override'),
                  dropdownMenuEntries: [
                    DropdownMenuEntry(value: 'One', label: 'One'),
                    DropdownMenuEntry(value: 'Two', label: 'Two'),
                    DropdownMenuEntry(value: 'Three', label: 'Three'),
                  ],
                ),
              ),
              TextField(controller: textEditingController, enabled: enabled),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _disable,
        tooltip: 'Update dropdown',
        child: const Icon(Icons.add),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}