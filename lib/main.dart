import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

// Custom DropdownMenu that fixes the disabled label styling issue
class FixedDropdownMenu<T> extends StatelessWidget {
  final bool enabled;
  final double? width;
  final T? initialSelection;
  final List<DropdownMenuEntry<T>> dropdownMenuEntries;
  final ValueChanged<T?>? onSelected;
  final Widget? label;
  final String? hintText;
  final String? helperText;
  final String? errorText;
  final Widget? leadingIcon;
  final Widget? trailingIcon;
  final bool showTrailingIcon;
  final InputDecorationTheme? inputDecorationTheme;

  const FixedDropdownMenu({
    super.key,
    this.enabled = true,
    this.width,
    this.initialSelection,
    required this.dropdownMenuEntries,
    this.onSelected,
    this.label,
    this.hintText,
    this.helperText,
    this.errorText,
    this.leadingIcon,
    this.trailingIcon,
    this.showTrailingIcon = true,
    this.inputDecorationTheme,
  });

  @override
  Widget build(BuildContext context) {
    // For disabled state, create a visual overlay that mimics TextField disabled appearance
    if (!enabled) {
      return Opacity(
        opacity: 0.6,
        child: IgnorePointer(
          child: DropdownMenu<T>(
            enabled: true, // Keep enabled internally to avoid conflicts
            width: width,
            initialSelection: initialSelection,
            onSelected: null, // Disable callbacks
            label: label != null
                ? DefaultTextStyle.merge(
                    style: TextStyle(color: Theme.of(context).disabledColor),
                    child: label!,
                  )
                : null,
            hintText: hintText,
            helperText: helperText,
            errorText: errorText,
            leadingIcon: leadingIcon,
            trailingIcon: trailingIcon,
            showTrailingIcon: showTrailingIcon,
            inputDecorationTheme: (inputDecorationTheme ?? const InputDecorationTheme()).copyWith(
              labelStyle: TextStyle(color: Theme.of(context).disabledColor),
              floatingLabelStyle: TextStyle(color: Theme.of(context).disabledColor),
              border: OutlineInputBorder(
                borderSide: BorderSide(color: Theme.of(context).disabledColor),
              ),
            ),
            dropdownMenuEntries: dropdownMenuEntries,
          ),
        ),
      );
    }

    // When enabled, use normal DropdownMenu
    return DropdownMenu<T>(
      enabled: enabled,
      width: width,
      initialSelection: initialSelection,
      onSelected: onSelected,
      label: label,
      hintText: hintText,
      helperText: helperText,
      errorText: errorText,
      leadingIcon: leadingIcon,
      trailingIcon: trailingIcon,
      showTrailingIcon: showTrailingIcon,
      inputDecorationTheme: inputDecorationTheme,
      dropdownMenuEntries: dropdownMenuEntries,
    );
  }
}



class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const MyHomePage(title: 'Flutter Demo Home Page'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  final TextEditingController textEditingController = TextEditingController(
    text: 'One',
  );
  bool enabled = true;

  void _disable() {
    setState(() {
      enabled = !enabled;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.title)),
      body: Center(
        child: Padding(
          padding: EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              // Original DropdownMenu (broken)
              const Text('Original DropdownMenu (broken):'),
              const SizedBox(height: 8),
              DropdownMenu<String>(
                width: double.infinity,
                enabled: enabled,
                initialSelection: 'One',
                label: const Text('Original DropdownMenu'),
                dropdownMenuEntries: [
                  DropdownMenuEntry(value: 'One', label: 'One'),
                  DropdownMenuEntry(value: 'Two', label: 'Two'),
                  DropdownMenuEntry(value: 'Three', label: 'Three'),
                ],
              ),

              const SizedBox(height: 20),

              // Fixed DropdownMenu
              const Text('Fixed DropdownMenu (working):'),
              const SizedBox(height: 8),
              FixedDropdownMenu<String>(
                width: double.infinity,
                enabled: enabled,
                initialSelection: 'One',
                label: const Text('Fixed DropdownMenu'),
                dropdownMenuEntries: [
                  DropdownMenuEntry(value: 'One', label: 'One'),
                  DropdownMenuEntry(value: 'Two', label: 'Two'),
                  DropdownMenuEntry(value: 'Three', label: 'Three'),
                ],
              ),

              const SizedBox(height: 20),

              // Comparison with TextField
              const Text('TextField for comparison:'),
              const SizedBox(height: 8),
              TextField(
                controller: textEditingController,
                enabled: enabled,
                decoration: const InputDecoration(
                  labelText: 'TextField for comparison',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _disable,
        tooltip: 'Update dropdown',
        child: const Icon(Icons.add),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}