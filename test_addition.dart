  testWidgets('DropdownMenu disabled state applies proper styling to label and selected value text', (
    WidgetTester tester,
  ) async {
    final ThemeData themeData = ThemeData();
    const String labelText = 'Test Label';
    
    // Test enabled state first
    await tester.pumpWidget(
      MaterialApp(
        theme: themeData,
        home: Scaffold(
          body: DropdownMenu<TestMenu>(
            enabled: true,
            label: const Text(labelText),
            initialSelection: TestMenu.mainMenu0,
            dropdownMenuEntries: menuChildren,
          ),
        ),
      ),
    );

    // Find the TextField and its EditableText
    final TextField enabledTextField = tester.widget(find.byType(TextField));
    final EditableText enabledEditableText = tester.widget(find.byType(EditableText));
    
    // Verify enabled state styling
    expect(enabledTextField.enabled, true);
    expect(enabledEditableText.style.color, isNot(themeData.disabledColor));
    
    // Test disabled state
    await tester.pumpWidget(
      MaterialApp(
        theme: themeData,
        home: Scaffold(
          body: DropdownMenu<TestMenu>(
            enabled: false,
            label: const Text(labelText),
            initialSelection: TestMenu.mainMenu0,
            dropdownMenuEntries: menuChildren,
          ),
        ),
      ),
    );

    // Find the TextField and its EditableText in disabled state
    final TextField disabledTextField = tester.widget(find.byType(TextField));
    final EditableText disabledEditableText = tester.widget(find.byType(EditableText));
    
    // Verify disabled state styling
    expect(disabledTextField.enabled, false);
    expect(disabledEditableText.style.color, themeData.disabledColor);
    
    // Verify the label has disabled styling applied
    final InputDecorator disabledInputDecorator = tester.widget(find.byType(InputDecorator));
    expect(disabledInputDecorator.decoration.label, isA<DefaultTextStyle>());
    
    final DefaultTextStyle labelDefaultTextStyle = disabledInputDecorator.decoration.label! as DefaultTextStyle;
    expect(labelDefaultTextStyle.style.color, themeData.disabledColor);
    expect(labelDefaultTextStyle.child, isA<Text>());
    
    final Text labelWidget = labelDefaultTextStyle.child as Text;
    expect(labelWidget.data, 'Test Label');
  });
